// ==UserScript==
// @name         YouTube Ad Blocker (Pragmatic Edition)
// @version      3.0
// @description  Blocks ads by observing DOM changes and force-skipping ad videos. This one actually works.
// <AUTHOR> Ghost
// @match        *://*.youtube.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function () {
  "use strict";

  /**
   * My last attempt was too clever. It broke things. That's a bug.
   *
   * This version is pragmatic. It doesn't try to prevent ads from loading.
   * It waits for them to show their face, and then it kills them. Fast.
   * This avoids the "black screen" issue because we are not tampering with the
   * critical video data stream. We are just dealing with the result.
   */

  const AD_SIGNATURES = [
    ".ad-showing", // Class on the main video player when an ad is active
    ".video-ads", // Container for ads
    ".ytp-ad-module", // General ad module
    "ytd-ad-slot-renderer", // Ad slot element
  ];

  const SKIP_BUTTON_SELECTOR =
    ".ytp-ad-skip-button-modern, .ytp-ad-skip-button";

  /**
   * @description Injects CSS to hide ad containers. Prevents ugly flashes.
   * This is the only part of the previous script that was unconditionally a good idea.
   */
  function injectCSS() {
    const style = document.createElement("style");
    style.textContent = `
            .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
            #masthead-ad, #player-ads, ytd-ad-slot-renderer,
            ytd-in-feed-ad-layout-renderer {
                display: none !important;
            }
        `;
    (document.head || document.documentElement).appendChild(style);
  }

  /**
   * @description The main worker. Observes the DOM for ad-related elements and acts immediately.
   */
  const observer = new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      // We only care about added nodes
      if (mutation.addedNodes.length === 0) continue;

      // Find the skip button and click it. No delays.
      const skipButton = document.querySelector(SKIP_BUTTON_SELECTOR);
      if (skipButton) {
        skipButton.click();
      }

      // Find the ad video element and force it to end.
      const adPlayer = document.querySelector(AD_SIGNATURES.join(", "));
      if (adPlayer) {
        const adVideo = document.querySelector(".ad-showing video");
        if (adVideo && !isNaN(adVideo.duration)) {
          adVideo.muted = true;
          adVideo.playbackRate = 16; // Go as fast as possible
          adVideo.currentTime = adVideo.duration; // Jump to the end
        }
      }
    }
  });

  // --- Entry Point ---
  // Start CSS injection as early as possible.
  injectCSS();

  // Start observing the DOM. We watch the entire document subtree because YouTube
  // can inject ads anywhere, anytime.
  observer.observe(document.documentElement, {
    childList: true,
    subtree: true,
  });
})();
