// ==UserScript==
// @name         Universal Ad Blocker (Safari Edition)
// @version      1.0
// @description  Global ad blocker with EasyList-style filtering for Safari UserScript environment
// <AUTHOR> Assistant
// @match        *://*/*
// @exclude      *://www.youtube.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== CONFIGURATION =====
    const CONFIG = {
        // Performance settings
        OBSERVER_THROTTLE: 200,
        CLEANUP_INTERVAL: 10000,
        BATCH_SIZE: 50,
        
        // Feature toggles
        ENABLE_LOGGING: false,
        ENABLE_NETWORK_BLOCKING: true,
        ENABLE_COSMETIC_FILTERING: true,
        ENABLE_PERFORMANCE_MONITORING: true,
        
        // Namespace to avoid conflicts with 002.js
        NAMESPACE: 'UAB_',
        
        // Common ad patterns (EasyList-inspired)
        AD_PATTERNS: {
            // URL patterns for network blocking
            NETWORK: [
                /\/ads?\//i,
                /\/ad[sv]\d*/i,
                /\/advertisement/i,
                /\/adnxs\./i,
                /\/adsystem/i,
                /\/adservice/i,
                /\/adserver/i,
                /\/adimg/i,
                /\/adform/i,
                /\/adsense/i,
                /\/doubleclick/i,
                /\/googlesyndication/i,
                /\/googletagmanager/i,
                /\/googletagservices/i,
                /\/amazon-adsystem/i,
                /\/facebook\.com\/tr/i,
                /\/analytics/i,
                /\/tracking/i,
                /\/metrics/i,
                /\/telemetry/i,
                /\/beacon/i,
                /\/pixel/i,
                /\/collect\?/i,
                /\/outbrain/i,
                /\/taboola/i,
                /\/criteo/i,
                /\/adsystem/i
            ],
            
            // CSS selectors for cosmetic filtering
            COSMETIC: [
                // Generic ad containers
                '[class*="ad-"]',
                '[class*="ads-"]',
                '[class*="advertisement"]',
                '[class*="banner"]',
                '[class*="popup"]',
                '[class*="overlay"]',
                '[class*="modal"][class*="ad"]',
                '[id*="ad-"]',
                '[id*="ads-"]',
                '[id*="advertisement"]',
                '[id*="banner"]',
                '[id*="popup"]',
                
                // Common ad networks
                '.adsbygoogle',
                '.ad-slot',
                '.ad-container',
                '.ad-wrapper',
                '.ad-banner',
                '.ad-block',
                '.ad-unit',
                '.sponsored',
                '.promotion',
                '.promo',
                
                // Social media ads
                '[data-testid*="ad"]',
                '[data-ad-slot]',
                '[data-ad-unit]',
                '[aria-label*="Sponsored"]',
                '[aria-label*="Advertisement"]',
                
                // Video ads
                '.video-ad',
                '.preroll',
                '.midroll',
                '.postroll',
                
                // Newsletter/subscription popups
                '.newsletter-popup',
                '.subscription-modal',
                '.email-signup',
                
                // Cookie banners (optional)
                '.cookie-banner',
                '.cookie-notice',
                '.gdpr-banner'
            ]
        }
    };

    // ===== PERFORMANCE MONITORING =====
    class PerformanceMonitor {
        constructor() {
            this.stats = {
                elementsBlocked: 0,
                requestsBlocked: 0,
                startTime: Date.now(),
                lastCleanup: Date.now()
            };
        }
        
        increment(metric) {
            this.stats[metric] = (this.stats[metric] || 0) + 1;
        }
        
        getStats() {
            return {
                ...this.stats,
                uptime: Date.now() - this.stats.startTime,
                elementsPerMinute: Math.round(this.stats.elementsBlocked / ((Date.now() - this.stats.startTime) / 60000))
            };
        }
    }

    const perfMonitor = CONFIG.ENABLE_PERFORMANCE_MONITORING ? new PerformanceMonitor() : null;

    // ===== LOGGING SYSTEM =====
    const logger = {
        log: (...args) => CONFIG.ENABLE_LOGGING && console.log(`[${CONFIG.NAMESPACE}AdBlock]`, ...args),
        warn: (...args) => CONFIG.ENABLE_LOGGING && console.warn(`[${CONFIG.NAMESPACE}AdBlock]`, ...args),
        error: (...args) => console.error(`[${CONFIG.NAMESPACE}AdBlock]`, ...args)
    };

    // ===== NETWORK REQUEST BLOCKER =====
    class NetworkBlocker {
        constructor() {
            this.blockedRequests = new Set();
            this.originalFetch = window.fetch;
            this.originalXHR = window.XMLHttpRequest;
            this.init();
        }
        
        init() {
            if (!CONFIG.ENABLE_NETWORK_BLOCKING) return;
            
            // Override fetch
            window.fetch = (...args) => {
                const url = args[0];
                if (this.shouldBlock(url)) {
                    perfMonitor?.increment('requestsBlocked');
                    logger.log('Blocked fetch request:', url);
                    return Promise.reject(new Error('Blocked by ad blocker'));
                }
                return this.originalFetch.apply(window, args);
            };
            
            // Override XMLHttpRequest
            const self = this;
            window.XMLHttpRequest = function() {
                const xhr = new self.originalXHR();
                const originalOpen = xhr.open;
                
                xhr.open = function(method, url, ...args) {
                    if (self.shouldBlock(url)) {
                        perfMonitor?.increment('requestsBlocked');
                        logger.log('Blocked XHR request:', url);
                        // Return a dummy response
                        setTimeout(() => {
                            if (xhr.onerror) xhr.onerror();
                        }, 0);
                        return;
                    }
                    return originalOpen.apply(xhr, [method, url, ...args]);
                };
                
                return xhr;
            };
            
            // Copy static properties
            Object.setPrototypeOf(window.XMLHttpRequest, this.originalXHR);
            Object.setPrototypeOf(window.XMLHttpRequest.prototype, this.originalXHR.prototype);
        }
        
        shouldBlock(url) {
            if (!url || typeof url !== 'string') return false;
            
            return CONFIG.AD_PATTERNS.NETWORK.some(pattern => {
                const matches = pattern.test(url);
                if (matches) {
                    this.blockedRequests.add(url);
                }
                return matches;
            });
        }
        
        getBlockedCount() {
            return this.blockedRequests.size;
        }
    }

    // ===== COSMETIC FILTER =====
    class CosmeticFilter {
        constructor() {
            this.processedElements = new WeakSet();
            this.hiddenElements = new Set();
            this.lastProcessTime = 0;
            this.injectBaseCSS();
        }
        
        injectBaseCSS() {
            if (document.getElementById(`${CONFIG.NAMESPACE}ad-block-styles`)) return;
            
            const style = document.createElement('style');
            style.id = `${CONFIG.NAMESPACE}ad-block-styles`;
            style.textContent = this.generateCSS();
            
            (document.head || document.documentElement).appendChild(style);
            logger.log('Base CSS injected');
        }
        
        generateCSS() {
            return CONFIG.AD_PATTERNS.COSMETIC.map(selector => {
                return `${selector} { display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; width: 0 !important; margin: 0 !important; padding: 0 !important; }`;
            }).join('\n');
        }
        
        shouldProcess() {
            const now = Date.now();
            if (now - this.lastProcessTime < CONFIG.OBSERVER_THROTTLE) {
                return false;
            }
            this.lastProcessTime = now;
            return true;
        }
        
        processElements() {
            if (!this.shouldProcess()) return;
            
            let processed = 0;
            
            CONFIG.AD_PATTERNS.COSMETIC.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (!this.processedElements.has(element) && processed < CONFIG.BATCH_SIZE) {
                            this.hideElement(element);
                            this.processedElements.add(element);
                            processed++;
                        }
                    });
                } catch (error) {
                    logger.error('Error processing selector:', selector, error);
                }
            });
            
            if (processed > 0) {
                perfMonitor?.increment('elementsBlocked');
                logger.log(`Processed ${processed} ad elements`);
            }
        }
        
        hideElement(element) {
            if (!element || element.style.display === 'none') return;
            
            // Multiple hiding strategies
            element.style.setProperty('display', 'none', 'important');
            element.style.setProperty('visibility', 'hidden', 'important');
            element.style.setProperty('opacity', '0', 'important');
            element.style.setProperty('height', '0', 'important');
            element.style.setProperty('width', '0', 'important');
            element.style.setProperty('margin', '0', 'important');
            element.style.setProperty('padding', '0', 'important');
            
            // Add to hidden set for tracking
            this.hiddenElements.add(element);
            
            // Remove from DOM if it's clearly an ad container
            if (this.isAdContainer(element)) {
                element.remove();
            }
        }
        
        isAdContainer(element) {
            const tagName = element.tagName.toLowerCase();
            const className = element.className || '';
            const id = element.id || '';
            
            // Check for obvious ad containers
            const adIndicators = ['ad', 'ads', 'advertisement', 'banner', 'sponsored', 'promo'];
            
            return adIndicators.some(indicator => 
                className.toLowerCase().includes(indicator) || 
                id.toLowerCase().includes(indicator)
            ) && ['div', 'section', 'aside', 'iframe'].includes(tagName);
        }
        
        cleanup() {
            // Remove references to elements that are no longer in DOM
            this.hiddenElements.forEach(element => {
                if (!document.contains(element)) {
                    this.hiddenElements.delete(element);
                }
            });
        }
    }

    // ===== MAIN CONTROLLER =====
    class UniversalAdBlocker {
        constructor() {
            this.networkBlocker = new NetworkBlocker();
            this.cosmeticFilter = new CosmeticFilter();
            this.isInitialized = false;
            this.observerTimeout = null;
        }
        
        init() {
            if (this.isInitialized) return;
            
            logger.log('Universal Ad Blocker initializing...');
            
            // Initial processing
            this.cosmeticFilter.processElements();
            
            // Set up mutation observer
            this.setupObserver();
            
            // Set up periodic cleanup
            setInterval(() => {
                this.cosmeticFilter.cleanup();
                if (perfMonitor) {
                    logger.log('Performance stats:', perfMonitor.getStats());
                }
            }, CONFIG.CLEANUP_INTERVAL);
            
            this.isInitialized = true;
            logger.log('Universal Ad Blocker initialized');
        }
        
        setupObserver() {
            const debouncedProcess = () => {
                clearTimeout(this.observerTimeout);
                this.observerTimeout = setTimeout(() => {
                    this.cosmeticFilter.processElements();
                }, CONFIG.OBSERVER_THROTTLE);
            };
            
            const observer = new MutationObserver((mutations) => {
                const hasRelevantChanges = mutations.some(mutation => 
                    mutation.addedNodes.length > 0
                );
                
                if (hasRelevantChanges) {
                    debouncedProcess();
                }
            });
            
            observer.observe(document.documentElement, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });
        }
    }

    // ===== INITIALIZATION =====
    const adBlocker = new UniversalAdBlocker();
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => adBlocker.init());
    } else {
        adBlocker.init();
    }
    
    // Backup initialization
    setTimeout(() => adBlocker.init(), 1000);
    
    // Global error handling
    window.addEventListener('error', (event) => {
        if (event.error && event.error.stack && event.error.stack.includes(CONFIG.NAMESPACE)) {
            logger.error('Global error caught:', event.error);
        }
    });
    
    // ===== ADVANCED FEATURES =====

    // Anti-circumvention measures
    class AntiCircumvention {
        constructor() {
            this.setupProtections();
        }

        setupProtections() {
            // Protect against ad blocker detection
            this.protectConsole();
            this.protectGetComputedStyle();
            this.protectQuerySelector();
        }

        protectConsole() {
            // Prevent ad blocker detection via console manipulation
            const originalLog = console.log;
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('adblock') || message.includes('ad blocker')) {
                    return; // Suppress ad blocker detection logs
                }
                return originalLog.apply(console, args);
            };
        }

        protectGetComputedStyle() {
            // Prevent detection via getComputedStyle
            const originalGetComputedStyle = window.getComputedStyle;
            window.getComputedStyle = function(element, pseudoElement) {
                const styles = originalGetComputedStyle.call(window, element, pseudoElement);

                // If element is hidden by ad blocker, fake visible styles
                if (element && element.style.display === 'none' &&
                    adBlocker.cosmeticFilter.hiddenElements.has(element)) {

                    return new Proxy(styles, {
                        get(target, prop) {
                            if (prop === 'display') return 'block';
                            if (prop === 'visibility') return 'visible';
                            if (prop === 'opacity') return '1';
                            return target[prop];
                        }
                    });
                }

                return styles;
            };
        }

        protectQuerySelector() {
            // Prevent detection via querySelector manipulation
            const originalQuerySelector = document.querySelector;
            const originalQuerySelectorAll = document.querySelectorAll;

            document.querySelector = function(selector) {
                const result = originalQuerySelector.call(document, selector);

                // If querying for ad elements that we've hidden, return null
                if (result && adBlocker.cosmeticFilter.hiddenElements.has(result)) {
                    return null;
                }

                return result;
            };

            document.querySelectorAll = function(selector) {
                const results = originalQuerySelectorAll.call(document, selector);

                // Filter out hidden ad elements
                return Array.from(results).filter(element =>
                    !adBlocker.cosmeticFilter.hiddenElements.has(element)
                );
            };
        }
    }

    // Site-specific handlers
    class SiteSpecificHandlers {
        constructor() {
            this.hostname = window.location.hostname;
            this.setupHandlers();
        }

        setupHandlers() {
            // Common social media sites
            if (this.hostname.includes('facebook.com')) {
                this.handleFacebook();
            } else if (this.hostname.includes('twitter.com') || this.hostname.includes('x.com')) {
                this.handleTwitter();
            } else if (this.hostname.includes('instagram.com')) {
                this.handleInstagram();
            } else if (this.hostname.includes('linkedin.com')) {
                this.handleLinkedIn();
            } else if (this.hostname.includes('reddit.com')) {
                this.handleReddit();
            }

            // News sites
            if (this.hostname.includes('cnn.com') ||
                this.hostname.includes('bbc.com') ||
                this.hostname.includes('nytimes.com')) {
                this.handleNewsSites();
            }
        }

        handleFacebook() {
            const fbSelectors = [
                '[data-pagelet="RightRail"]',
                '[data-pagelet="FeedAds"]',
                '[aria-label*="Sponsored"]',
                '[data-testid="story-subtilte"] span:contains("Sponsored")'
            ];

            this.addCustomSelectors(fbSelectors);
        }

        handleTwitter() {
            const twitterSelectors = [
                '[data-testid="placementTracking"]',
                '[data-testid="trend"] [aria-label*="Promoted"]',
                '[data-testid="tweet"] [data-testid="socialContext"]:contains("Promoted")'
            ];

            this.addCustomSelectors(twitterSelectors);
        }

        handleInstagram() {
            const igSelectors = [
                '[data-testid="ad-context-link"]',
                'article [role="button"]:contains("Sponsored")'
            ];

            this.addCustomSelectors(igSelectors);
        }

        handleLinkedIn() {
            const linkedInSelectors = [
                '.feed-shared-update-v2--promoted',
                '.ad-banner-container',
                '[data-test-id="sponsored-update"]'
            ];

            this.addCustomSelectors(linkedInSelectors);
        }

        handleReddit() {
            const redditSelectors = [
                '[data-testid="post-container"] [data-adclicklocation]',
                '.promotedlink',
                '[data-promoted="true"]'
            ];

            this.addCustomSelectors(redditSelectors);
        }

        handleNewsSites() {
            const newsSelectors = [
                '.advertisement',
                '.ad-container',
                '.sponsored-content',
                '.native-ad',
                '[class*="outbrain"]',
                '[class*="taboola"]'
            ];

            this.addCustomSelectors(newsSelectors);
        }

        addCustomSelectors(selectors) {
            CONFIG.AD_PATTERNS.COSMETIC.push(...selectors);
            logger.log(`Added ${selectors.length} site-specific selectors for ${this.hostname}`);
        }
    }

    // Initialize advanced features
    const antiCircumvention = new AntiCircumvention();
    const siteHandlers = new SiteSpecificHandlers();

    // Expose API for debugging (only in development)
    if (CONFIG.ENABLE_LOGGING) {
        window[`${CONFIG.NAMESPACE}AdBlocker`] = {
            getStats: () => perfMonitor?.getStats(),
            getBlockedRequests: () => adBlocker.networkBlocker.getBlockedCount(),
            getHiddenElements: () => adBlocker.cosmeticFilter.hiddenElements.size,
            toggleLogging: () => { CONFIG.ENABLE_LOGGING = !CONFIG.ENABLE_LOGGING; },
            forceCleanup: () => adBlocker.cosmeticFilter.cleanup()
        };
    }

    logger.log('Universal Ad Blocker script loaded with advanced features');

})();
