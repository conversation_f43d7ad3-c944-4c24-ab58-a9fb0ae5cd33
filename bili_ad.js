// ==UserScript==
// @name         Bilibili Ad Blocker (Specialized Edition)
// @version      1.0
// @description  Specialized Bilibili ad blocker with precise UI protection and advanced ad detection
// <AUTHOR> Assistant Enhanced
// @match        *://bilibili.com/*
// @match        *://*.bilibili.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    // ===== BILIBILI-SPECIFIC CONFIGURATION =====
    const BILI_CONFIG = {
        // Performance settings optimized for Bilibili
        OBSERVER_THROTTLE: 150,
        CLEANUP_INTERVAL: 8000,
        BATCH_SIZE: 40,
        
        // Feature toggles
        ENABLE_LOGGING: false,
        ENABLE_PERFORMANCE_MONITORING: true,
        ENABLE_ANTI_DETECTION: true,
        ENABLE_DYNAMIC_LOADING: true,
        
        // Namespace
        NAMESPACE: 'BILI_',
        
        // Bilibili-specific selectors
        PROTECTED_ELEMENTS: [
            // Core page structure
            '.header', '.bili-header', '.international-header',
            '.nav', '.navigation', '.nav-menu', '.nav-item',
            '.left-entry', '.right-entry', '.channel-menu',
            
            // Banners and carousels (non-ad)
            '.banner:not(.ad-banner):not([class*="ad-"]):not([data-report*="ad"])',
            '.carousel:not(.ad-carousel)',
            '.carousel-banner:not([class*="ad-"])',
            '.home-banner:not([class*="ad-"])',
            '.top-banner:not([class*="ad-"])',
            '.bili-banner:not([class*="ad-"])',
            '.video-banner:not([class*="ad-"])',
            '.live-banner:not([class*="ad-"])',
            '.pgc-banner:not([class*="ad-"])',
            
            // Video content
            '.video-card', '.bili-video-card', '.small-item',
            '.video-item', '.feed-card', '.recommend-item',
            '.video-page-card-small', '.bili-video-card__info',
            '.bili-video-card__wrap', '.video-card-info',
            
            // User interface
            '.user-panel', '.user-info', '.user-avatar', '.user-name',
            '.search-panel', '.search-container', '.center-search-container',
            '.main-content', '.content-wrapper', '.recommended-container',
            '.bili-feed4', '.feed4-layout', '.feed-roll-btn',
            
            // Player and media
            '.bilibili-player', '.bpx-player', '.video-info', '.video-desc',
            '.comment', '.reply-box', '.bb-comment',
            '.right-container', '.sidebar', '.rec-list',
            
            // Generic protection patterns
            '[class*="bili-"]:not([class*="ad"]):not([class*="advertisement"])',
            '[class*="video-"]:not([class*="ad"]):not([class*="advertisement"])',
            '[class*="feed-"]:not([class*="ad"]):not([class*="advertisement"])',
            '[class*="recommend-"]:not([class*="ad"])',
            '[class*="card-"]:not([class*="ad"])'
        ],
        
        AD_SELECTORS: [
            // Explicit ad identifiers
            '.ad-report', '.advertisement', '.advertisement-banner',
            '.commercial-banner', '.sponsor-banner:not(.user-sponsor)',
            '.ad-banner', '.ad-container:not(.video-container)',
            '.ad-wrapper:not(.video-wrapper)', '.ad-card:not(.video-card)',
            '.ad-floor-exp', '.ad-creative', '.ad-material',
            '.ad-slot:not(.video-slot)', '.ad-unit:not(.video-unit)',
            
            // Data attribute ads
            '[data-report*="ad"]:not([class*="video"]):not([class*="feed"])',
            '[data-ad-creative-type]', '[data-ad-slot]:not([class*="video"])',
            '[data-spm*="ad"]:not([class*="video"])', '[data-ad-type]',
            
            // Eva advertising system
            '.eva-banner[data-loc*="ad"]', '.eva-extension',
            '.eva-creative', '.eva-material',
            
            // Feed and recommendation ads
            '.feed-ad-container', '.recommend-ad-card',
            '.live-ad-card', '.pgc-ad-card', '.channel-ad-card',
            
            // Popup and modal ads
            '.popup-ad', '.modal-ad', '.dialog-ad', '.overlay-ad',
            '.toast-ad', '.notification-ad',
            
            // Video ads
            '.video-ad-container', '.player-ad', '.preroll-ad',
            '.midroll-ad', '.postroll-ad', '.video-ad-overlay',
            
            // Sidebar and banner ads
            '.sidebar-ad', '.right-ad', '.bottom-ad', '.top-ad',
            '.header-ad', '.footer-ad',
            
            // Generic ad patterns (with exclusions)
            '[class*="ad-"]:not(.video-card):not(.feed-card):not(.bili-card)',
            '[id*="ad-"]:not([id*="video"]):not([id*="feed"]):not([id*="bili"])',
            '[class*="advertisement"]:not([class*="video"])',
            '[class*="commercial"]:not([class*="video"])',
            '[class*="sponsor"]:not(.user-sponsor):not([class*="video"])',
            
            // Third-party ad networks
            '.adsbygoogle', '.ad-by-google', '.google-ad',
            '[class*="outbrain"]', '[class*="taboola"]',
            '[class*="criteo"]', '[class*="doubleclick"]'
        ]
    };

    // ===== PERFORMANCE MONITORING =====
    class BilibiliPerformanceMonitor {
        constructor() {
            this.stats = {
                adsBlocked: 0,
                elementsProtected: 0,
                observerCalls: 0,
                startTime: Date.now(),
                lastCleanup: Date.now()
            };
        }
        
        increment(metric) {
            this.stats[metric] = (this.stats[metric] || 0) + 1;
        }
        
        getStats() {
            return {
                ...this.stats,
                uptime: Date.now() - this.stats.startTime,
                adsPerMinute: Math.round(this.stats.adsBlocked / ((Date.now() - this.stats.startTime) / 60000))
            };
        }
    }

    const perfMonitor = BILI_CONFIG.ENABLE_PERFORMANCE_MONITORING ? new BilibiliPerformanceMonitor() : null;

    // ===== LOGGING SYSTEM =====
    const logger = {
        log: (...args) => BILI_CONFIG.ENABLE_LOGGING && console.log(`[${BILI_CONFIG.NAMESPACE}AdBlock]`, ...args),
        warn: (...args) => BILI_CONFIG.ENABLE_LOGGING && console.warn(`[${BILI_CONFIG.NAMESPACE}AdBlock]`, ...args),
        error: (...args) => console.error(`[${BILI_CONFIG.NAMESPACE}AdBlock]`, ...args)
    };

    // ===== BILIBILI AD BLOCKER CORE =====
    class BilibiliAdBlocker {
        constructor() {
            this.processedElements = new WeakSet();
            this.protectedElements = new WeakSet();
            this.hiddenAds = new Set();
            this.lastProcessTime = 0;
            this.isInitialized = false;
        }
        
        init() {
            if (this.isInitialized) return;
            
            logger.log('Bilibili Ad Blocker initializing...');
            
            // Inject Bilibili-specific CSS
            this.injectBilibiliCSS();
            
            // Setup observers
            this.setupMainObserver();
            this.setupDynamicObserver();
            
            // Initial processing
            this.processPage();
            
            // Setup cleanup interval
            setInterval(() => this.cleanup(), BILI_CONFIG.CLEANUP_INTERVAL);
            
            this.isInitialized = true;
            logger.log('Bilibili Ad Blocker initialized successfully');
        }
        
        injectBilibiliCSS() {
            if (document.getElementById(`${BILI_CONFIG.NAMESPACE}ad-block-styles`)) return;
            
            const style = document.createElement('style');
            style.id = `${BILI_CONFIG.NAMESPACE}ad-block-styles`;
            style.textContent = this.generateBilibiliCSS();
            
            (document.head || document.documentElement).appendChild(style);
            logger.log('Bilibili-specific CSS injected');
        }
        
        generateBilibiliCSS() {
            const adSelectors = BILI_CONFIG.AD_SELECTORS.join(',\n');
            
            return `
                /* Bilibili Ad Blocking Styles */
                ${adSelectors} {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    height: 0 !important;
                    width: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                    position: absolute !important;
                    left: -9999px !important;
                    top: -9999px !important;
                }
                
                /* Ensure protected elements remain visible */
                .video-card, .bili-video-card, .feed-card,
                .recommend-item, .small-item, .video-item,
                .banner:not(.ad-banner), .bili-banner:not(.ad-banner),
                .video-banner, .carousel:not(.ad-carousel),
                .header, .bili-header, .nav, .navigation {
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    position: static !important;
                }
                
                /* Fix potential layout issues */
                .bili-feed4 .feed-card,
                .recommended-container .video-card,
                .rec-list .small-item {
                    display: block !important;
                    visibility: visible !important;
                }
            `;
        }
        
        shouldProcess() {
            const now = Date.now();
            if (now - this.lastProcessTime < BILI_CONFIG.OBSERVER_THROTTLE) {
                return false;
            }
            this.lastProcessTime = now;
            return true;
        }
        
        processPage() {
            if (!this.shouldProcess()) return;
            
            perfMonitor?.increment('observerCalls');
            
            let adsBlocked = 0;
            let elementsProtected = 0;
            
            // Process ad elements
            BILI_CONFIG.AD_SELECTORS.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (!this.processedElements.has(element) && this.isVisible(element)) {
                            this.hideAdElement(element);
                            this.processedElements.add(element);
                            adsBlocked++;
                        }
                    });
                } catch (error) {
                    logger.error('Error processing ad selector:', selector, error);
                }
            });
            
            // Protect normal elements
            BILI_CONFIG.PROTECTED_ELEMENTS.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (!this.protectedElements.has(element)) {
                            this.protectElement(element);
                            this.protectedElements.add(element);
                            elementsProtected++;
                        }
                    });
                } catch (error) {
                    logger.error('Error processing protected selector:', selector, error);
                }
            });
            
            if (adsBlocked > 0) {
                perfMonitor?.increment('adsBlocked');
                logger.log(`Blocked ${adsBlocked} ad elements`);
            }
            
            if (elementsProtected > 0) {
                perfMonitor?.increment('elementsProtected');
                logger.log(`Protected ${elementsProtected} normal elements`);
            }
        }
        
        isVisible(element) {
            return element.offsetParent !== null || 
                   element.offsetWidth > 0 || 
                   element.offsetHeight > 0;
        }
        
        hideAdElement(element) {
            // Multiple hiding strategies
            element.style.setProperty('display', 'none', 'important');
            element.style.setProperty('visibility', 'hidden', 'important');
            element.style.setProperty('opacity', '0', 'important');
            element.style.setProperty('height', '0', 'important');
            element.style.setProperty('width', '0', 'important');
            element.style.setProperty('margin', '0', 'important');
            element.style.setProperty('padding', '0', 'important');
            element.style.setProperty('overflow', 'hidden', 'important');
            element.style.setProperty('position', 'absolute', 'important');
            element.style.setProperty('left', '-9999px', 'important');
            element.style.setProperty('top', '-9999px', 'important');
            
            this.hiddenAds.add(element);
            
            // Remove obvious ad containers
            if (this.isObviousAd(element)) {
                element.remove();
            }
        }
        
        protectElement(element) {
            // Ensure protected elements remain visible
            if (element.style.display === 'none' && !this.hiddenAds.has(element)) {
                element.style.removeProperty('display');
            }
            if (element.style.visibility === 'hidden' && !this.hiddenAds.has(element)) {
                element.style.removeProperty('visibility');
            }
        }
        
        isObviousAd(element) {
            const className = element.className || '';
            const id = element.id || '';
            
            const adIndicators = ['advertisement', 'ad-banner', 'ad-container', 'commercial'];
            
            return adIndicators.some(indicator => 
                className.includes(indicator) || id.includes(indicator)
            );
        }
        
        setupMainObserver() {
            let observerTimeout;
            const debouncedProcess = () => {
                clearTimeout(observerTimeout);
                observerTimeout = setTimeout(() => {
                    this.processPage();
                }, BILI_CONFIG.OBSERVER_THROTTLE);
            };
            
            const observer = new MutationObserver((mutations) => {
                const hasRelevantChanges = mutations.some(mutation => {
                    return mutation.addedNodes.length > 0;
                });
                
                if (hasRelevantChanges) {
                    debouncedProcess();
                }
            });
            
            observer.observe(document.documentElement, {
                childList: true,
                subtree: true,
                attributes: false,
                characterData: false
            });
        }
        
        setupDynamicObserver() {
            if (!BILI_CONFIG.ENABLE_DYNAMIC_LOADING) return;
            
            // Watch for Bilibili's dynamic content loading
            const dynamicObserver = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check for feed updates
                            if (node.classList && (
                                node.classList.contains('feed-card') ||
                                node.classList.contains('video-card') ||
                                node.classList.contains('bili-video-card')
                            )) {
                                setTimeout(() => this.processPage(), 200);
                            }
                        }
                    });
                });
            });
            
            // Watch specific containers for dynamic loading
            const containers = [
                '.recommended-container',
                '.bili-feed4',
                '.feed4-layout',
                '.rec-list'
            ];
            
            containers.forEach(selector => {
                const container = document.querySelector(selector);
                if (container) {
                    dynamicObserver.observe(container, {
                        childList: true,
                        subtree: true
                    });
                }
            });
        }
        
        cleanup() {
            // Remove references to elements no longer in DOM
            this.hiddenAds.forEach(element => {
                if (!document.contains(element)) {
                    this.hiddenAds.delete(element);
                }
            });
            
            if (perfMonitor) {
                const stats = perfMonitor.getStats();
                logger.log('Performance stats:', stats);
            }
        }
    }

    // ===== ANTI-DETECTION MEASURES =====
    class BilibiliAntiDetection {
        constructor() {
            if (BILI_CONFIG.ENABLE_ANTI_DETECTION) {
                this.setupProtections();
            }
        }
        
        setupProtections() {
            // Protect against ad blocker detection
            this.protectConsole();
            this.protectGetComputedStyle();
        }
        
        protectConsole() {
            const originalLog = console.log;
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('adblock') || message.includes('广告屏蔽')) {
                    return;
                }
                return originalLog.apply(console, args);
            };
        }
        
        protectGetComputedStyle() {
            const originalGetComputedStyle = window.getComputedStyle;
            window.getComputedStyle = function(element, pseudoElement) {
                const styles = originalGetComputedStyle.call(window, element, pseudoElement);
                
                // If element is hidden by ad blocker, fake visible styles for detection
                if (element && adBlocker.hiddenAds.has(element)) {
                    return new Proxy(styles, {
                        get(target, prop) {
                            if (prop === 'display') return 'block';
                            if (prop === 'visibility') return 'visible';
                            if (prop === 'opacity') return '1';
                            return target[prop];
                        }
                    });
                }
                
                return styles;
            };
        }
    }

    // ===== INITIALIZATION =====
    const adBlocker = new BilibiliAdBlocker();
    const antiDetection = new BilibiliAntiDetection();
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => adBlocker.init());
    } else {
        adBlocker.init();
    }
    
    // Backup initialization
    setTimeout(() => adBlocker.init(), 1000);
    
    // Expose API for debugging
    if (BILI_CONFIG.ENABLE_LOGGING) {
        window[`${BILI_CONFIG.NAMESPACE}AdBlocker`] = {
            getStats: () => perfMonitor?.getStats(),
            getHiddenAds: () => adBlocker.hiddenAds.size,
            getProtectedElements: () => adBlocker.protectedElements,
            forceProcess: () => adBlocker.processPage(),
            forceCleanup: () => adBlocker.cleanup()
        };
    }
    
    logger.log('Bilibili Ad Blocker loaded successfully');

})();
