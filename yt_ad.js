// ==UserScript==
// @name         YouTube Ad Blocker (Specialized Edition)
// @version      5.0
// @description  Specialized YouTube ad blocker with advanced video ad handling and anti-detection
// <AUTHOR> Assistant Enhanced
// @match        *://www.youtube.com/*
// @match        *://*.youtube.com/*
// @run-at       document-start
// @grant        none
// ==/UserScript==

(function () {
  "use strict";

  // ===== CONFIGURATION =====
  const CONFIG = {
    // Performance settings
    OBSERVER_THROTTLE: 100, // ms between observer checks
    CLEANUP_INTERVAL: 5000, // ms between cleanup cycles
    MAX_RETRIES: 3,

    // Feature toggles
    ENABLE_LOGGING: false, // Set to false for production
    ENABLE_ANTI_DETECTION: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_SHORTS_HANDLING: true,

    // Selectors (updated for 2024)
    AD_SIGNATURES: [
      ".ad-showing",
      ".video-ads",
      ".ytp-ad-module",
      "ytd-ad-slot-renderer",
      ".ytp-ad-overlay-container",
      ".ytp-ad-text-overlay",
      ".ytp-ad-player-overlay",
      ".ytp-ad-image-overlay",
      "ytd-display-ad-renderer",
      "ytd-promoted-sparkles-web-renderer",
      "ytd-compact-promoted-video-renderer",
      "ytd-promoted-video-renderer",
      ".ytd-video-masthead-ad-v3-renderer",
      "tp-yt-iron-overlay-backdrop"
    ],

    SKIP_BUTTON_SELECTORS: [
      ".ytp-ad-skip-button-modern",
      ".ytp-ad-skip-button",
      ".ytp-skip-ad-button",
      "[class*='skip'][class*='button']",
      "[aria-label*='Skip']"
    ]
  };

  // ===== PERFORMANCE MONITORING =====
  class PerformanceMonitor {
    constructor() {
      this.metrics = {
        adsBlocked: 0,
        skipButtonClicks: 0,
        observerCalls: 0,
        lastCleanup: Date.now()
      };
    }

    increment(metric) {
      this.metrics[metric] = (this.metrics[metric] || 0) + 1;
    }

    getStats() {
      return { ...this.metrics, uptime: Date.now() - this.metrics.lastCleanup };
    }
  }

  const perfMonitor = CONFIG.ENABLE_PERFORMANCE_MONITORING ? new PerformanceMonitor() : null;

  // ===== LOGGING SYSTEM =====
  const logger = {
    log: (...args) => CONFIG.ENABLE_LOGGING && console.log('[YT-AdBlock]', ...args),
    warn: (...args) => CONFIG.ENABLE_LOGGING && console.warn('[YT-AdBlock]', ...args),
    error: (...args) => console.error('[YT-AdBlock]', ...args)
  };

  // ===== ENHANCED CSS INJECTION =====
  function injectEnhancedCSS() {
    if (document.getElementById('yt-adblock-styles')) return;

    const style = document.createElement("style");
    style.id = 'yt-adblock-styles';
    style.textContent = `
      /* Core ad blocking styles */
      .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
      .ytp-ad-text-overlay, .ytp-ad-player-overlay, .ytp-ad-image-overlay,
      #masthead-ad, #player-ads, ytd-ad-slot-renderer,
      ytd-in-feed-ad-layout-renderer, ytd-display-ad-renderer,
      ytd-promoted-sparkles-web-renderer, ytd-compact-promoted-video-renderer,
      ytd-promoted-video-renderer, .ytd-video-masthead-ad-v3-renderer,
      tp-yt-iron-overlay-backdrop, ytd-statement-banner-renderer,
      yt-mealbar-promo-renderer, ytd-banner-promo-renderer-background,
      [class*="ad-slot"], [class*="promoted"], [id*="ad-"],
      [data-ad-slot-id], .ytp-ad-progress-list {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      /* Prevent ad container flashing */
      .html5-video-player .ytp-ad-module {
        transition: none !important;
      }

      /* Enhanced shorts ad blocking */
      ytd-shorts ytd-ad-slot-renderer,
      ytd-shorts [class*="ad-"],
      #shorts-player .ytp-ad-module {
        display: none !important;
      }
    `;

    (document.head || document.documentElement).appendChild(style);
    logger.log('Enhanced CSS injected');
  }

  // ===== ANTI-DETECTION MEASURES =====
  class AntiDetection {
    constructor() {
      this.randomDelay = () => Math.random() * 50 + 10; // 10-60ms random delay
      this.humanLikeClick = this.humanLikeClick.bind(this);
    }

    // Simulate human-like clicking behavior
    humanLikeClick(element) {
      if (!element) return false;

      setTimeout(() => {
        // Create more realistic mouse events
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2 + (Math.random() - 0.5) * 10;
        const y = rect.top + rect.height / 2 + (Math.random() - 0.5) * 10;

        ['mousedown', 'mouseup', 'click'].forEach(eventType => {
          const event = new MouseEvent(eventType, {
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y
          });
          element.dispatchEvent(event);
        });

        perfMonitor?.increment('skipButtonClicks');
        logger.log('Skip button clicked with human-like behavior');
      }, this.randomDelay());

      return true;
    }

    // Randomize execution timing
    randomizedExecution(callback, baseDelay = 100) {
      setTimeout(callback, baseDelay + this.randomDelay());
    }
  }

  const antiDetection = CONFIG.ENABLE_ANTI_DETECTION ? new AntiDetection() : null;

  // ===== ENHANCED AD DETECTION AND REMOVAL =====
  class AdBlocker {
    constructor() {
      this.lastProcessTime = 0;
      this.processedElements = new WeakSet();
      this.retryCount = 0;
    }

    // Throttled processing to improve performance
    shouldProcess() {
      const now = Date.now();
      if (now - this.lastProcessTime < CONFIG.OBSERVER_THROTTLE) {
        return false;
      }
      this.lastProcessTime = now;
      return true;
    }

    // Enhanced skip button detection and clicking
    handleSkipButtons() {
      for (const selector of CONFIG.SKIP_BUTTON_SELECTORS) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
          if (!this.processedElements.has(button) && button.offsetParent !== null) {
            this.processedElements.add(button);

            if (antiDetection) {
              antiDetection.humanLikeClick(button);
            } else {
              button.click();
              perfMonitor?.increment('skipButtonClicks');
            }

            logger.log(`Skip button found and clicked: ${selector}`);
          }
        });
      }
    }

    // Enhanced ad video handling
    handleAdVideos() {
      const adVideos = document.querySelectorAll('video');
      adVideos.forEach(video => {
        if (this.processedElements.has(video)) return;

        // Check if this is an ad video
        const isAdVideo = CONFIG.AD_SIGNATURES.some(selector => {
          const parent = video.closest(selector);
          return parent !== null;
        });

        if (isAdVideo || video.src?.includes('googlevideo.com/videoplayback')) {
          this.processedElements.add(video);
          this.processAdVideo(video);
        }
      });
    }

    processAdVideo(video) {
      try {
        // Multiple strategies for ad video handling
        video.muted = true;
        video.volume = 0;

        if (!isNaN(video.duration) && video.duration > 0) {
          // Strategy 1: Jump to end
          video.currentTime = video.duration;

          // Strategy 2: Increase playback rate
          video.playbackRate = Math.min(16, video.playbackRate * 4);

          // Strategy 3: Pause and hide
          setTimeout(() => {
            video.pause();
            video.style.display = 'none';
          }, 100);
        }

        perfMonitor?.increment('adsBlocked');
        logger.log('Ad video processed and neutralized');

      } catch (error) {
        logger.error('Error processing ad video:', error);
      }
    }

    // Handle YouTube Shorts ads
    handleShortsAds() {
      if (!CONFIG.ENABLE_SHORTS_HANDLING || !window.location.pathname.includes('/shorts/')) {
        return;
      }

      const shortsAds = document.querySelectorAll('#shorts-player .ytp-ad-module, ytd-shorts ytd-ad-slot-renderer');
      shortsAds.forEach(ad => {
        if (!this.processedElements.has(ad)) {
          this.processedElements.add(ad);
          ad.remove();
          logger.log('Shorts ad removed');
        }
      });
    }

    // Main processing function
    process() {
      if (!this.shouldProcess()) return;

      perfMonitor?.increment('observerCalls');

      try {
        this.handleSkipButtons();
        this.handleAdVideos();
        this.handleShortsAds();
        this.retryCount = 0; // Reset on successful processing
      } catch (error) {
        logger.error('Error in ad processing:', error);
        this.retryCount++;

        if (this.retryCount >= CONFIG.MAX_RETRIES) {
          logger.error('Max retries reached, pausing processing');
          setTimeout(() => { this.retryCount = 0; }, 5000);
        }
      }
    }
  }

  const adBlocker = new AdBlocker();

  // ===== ENHANCED MUTATION OBSERVER =====
  let observerTimeout;
  const debouncedProcess = () => {
    clearTimeout(observerTimeout);
    observerTimeout = setTimeout(() => {
      adBlocker.process();
    }, CONFIG.OBSERVER_THROTTLE);
  };

  const observer = new MutationObserver((mutations) => {
    // Quick filter for relevant mutations
    const hasRelevantChanges = mutations.some(mutation => {
      if (mutation.addedNodes.length === 0) return false;

      // Check if any added nodes are relevant
      return Array.from(mutation.addedNodes).some(node => {
        if (node.nodeType !== Node.ELEMENT_NODE) return false;

        // Check for ad-related elements
        return CONFIG.AD_SIGNATURES.some(selector => {
          try {
            return node.matches?.(selector) || node.querySelector?.(selector);
          } catch (e) {
            return false;
          }
        });
      });
    });

    if (hasRelevantChanges) {
      debouncedProcess();
    }
  });

  // ===== CLEANUP AND MAINTENANCE =====
  function performCleanup() {
    try {
      // Remove any lingering ad elements
      CONFIG.AD_SIGNATURES.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          if (el.offsetParent !== null) { // Only remove visible elements
            el.remove();
          }
        });
      });

      // Clean up processed elements WeakSet periodically
      if (adBlocker.processedElements) {
        // WeakSet automatically cleans up when elements are garbage collected
        logger.log('Cleanup cycle completed');
      }

      perfMonitor && logger.log('Performance stats:', perfMonitor.getStats());

    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }

  // ===== URL CHANGE DETECTION =====
  let currentUrl = window.location.href;
  function handleUrlChange() {
    if (window.location.href !== currentUrl) {
      currentUrl = window.location.href;
      logger.log('URL changed, reinitializing...');

      // Reset processed elements for new page
      adBlocker.processedElements = new WeakSet();

      // Immediate processing for new page
      setTimeout(() => {
        adBlocker.process();
      }, 500);
    }
  }

  // ===== INITIALIZATION =====
  function initialize() {
    logger.log('YouTube Ad Blocker Enhanced Safari Edition v4.0 starting...');

    // Inject CSS as early as possible
    injectEnhancedCSS();

    // Start observing DOM changes
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true,
      attributes: false, // Optimize by not watching attributes
      characterData: false // Optimize by not watching text changes
    });

    // Initial processing
    adBlocker.process();

    // Set up periodic cleanup
    setInterval(performCleanup, CONFIG.CLEANUP_INTERVAL);

    // Set up URL change detection
    setInterval(handleUrlChange, 1000);

    // Performance monitoring
    if (perfMonitor) {
      setInterval(() => {
        const stats = perfMonitor.getStats();
        if (stats.adsBlocked > 0 || stats.skipButtonClicks > 0) {
          logger.log(`Performance: ${stats.adsBlocked} ads blocked, ${stats.skipButtonClicks} skip buttons clicked`);
        }
      }, 30000); // Log every 30 seconds
    }

    logger.log('Initialization complete');
  }

  // ===== ENTRY POINT =====
  // Initialize immediately if DOM is ready, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

  // Backup initialization for edge cases
  setTimeout(initialize, 1000);

  // ===== GLOBAL ERROR HANDLING =====
  window.addEventListener('error', (event) => {
    if (event.error && event.error.stack && event.error.stack.includes('YT-AdBlock')) {
      logger.error('Global error caught:', event.error);
    }
  });

})();
