# Universal Ad Blocker (003.js) 使用指南

## 概述
`003.js` 是一个全局广告屏蔽脚本，设计用于 Safari UserScript 环境，实现类似 AdBlock 的功能。它与 `002.js` (YouTube专用) 完全兼容，不会产生冲突。

## 主要特性

### 🌐 **全局覆盖**
- 支持所有网站（除了YouTube，避免与002.js冲突）
- 基于EasyList规则的广告检测
- 智能网络请求拦截
- CSS样式注入屏蔽

### 🚀 **高性能设计**
- 节流和防抖机制
- 批量处理优化
- WeakSet内存管理
- 性能监控系统

### 🛡️ **反检测技术**
- 防止广告屏蔽器检测
- 保护关键API调用
- 伪装隐藏元素状态
- 智能日志过滤

### 🎯 **站点特定优化**
- Facebook/Meta广告屏蔽
- Twitter/X推广内容过滤
- Instagram赞助内容移除
- LinkedIn广告屏蔽
- Reddit推广帖子过滤
- 新闻网站广告清理

## 配置选项

### 基础配置
```javascript
const CONFIG = {
    // 性能设置
    OBSERVER_THROTTLE: 200,     // 观察器节流间隔(ms)
    CLEANUP_INTERVAL: 10000,    // 清理周期(ms)
    BATCH_SIZE: 50,             // 批处理大小
    
    // 功能开关
    ENABLE_LOGGING: false,              // 调试日志
    ENABLE_NETWORK_BLOCKING: true,      // 网络请求拦截
    ENABLE_COSMETIC_FILTERING: true,    // CSS样式屏蔽
    ENABLE_PERFORMANCE_MONITORING: true // 性能监控
};
```

### 自定义广告模式
```javascript
// 添加自定义URL拦截模式
CONFIG.AD_PATTERNS.NETWORK.push(/\/custom-ad-pattern/i);

// 添加自定义CSS选择器
CONFIG.AD_PATTERNS.COSMETIC.push('.custom-ad-class');
```

## 使用方法

### 1. 安装脚本
1. 在Safari中安装UserScript扩展（如Tampermonkey）
2. 创建新脚本，复制003.js内容
3. 保存并启用脚本

### 2. 验证运行
打开任意网站，在控制台中输入：
```javascript
// 查看屏蔽统计（仅在调试模式下可用）
UAB_AdBlocker.getStats()
```

### 3. 性能调优
根据设备性能调整配置：

**高性能设备**：
```javascript
OBSERVER_THROTTLE: 100,
CLEANUP_INTERVAL: 5000,
BATCH_SIZE: 100
```

**低性能设备**：
```javascript
OBSERVER_THROTTLE: 500,
CLEANUP_INTERVAL: 20000,
BATCH_SIZE: 20
```

## 兼容性说明

### 与002.js的兼容性
- **命名空间隔离**：使用`UAB_`前缀避免冲突
- **域名排除**：自动排除YouTube域名
- **独立运行**：两个脚本可以同时运行

### 浏览器兼容性
- ✅ Safari (主要目标)
- ✅ Chrome/Edge (Tampermonkey)
- ✅ Firefox (Greasemonkey)
- ⚠️ 移动端Safari (功能受限)

## 屏蔽效果

### 网络级别屏蔽
- Google Ads (DoubleClick, AdSense)
- Facebook Pixel
- Amazon广告系统
- 第三方追踪脚本
- 分析和遥测数据

### 页面元素屏蔽
- 横幅广告
- 弹窗广告
- 视频前贴片
- 赞助内容
- 推广帖子
- Cookie横幅（可选）

### 社交媒体特定
- Facebook赞助帖子和右侧广告
- Twitter推广推文和趋势
- Instagram赞助内容
- LinkedIn广告和推广更新
- Reddit推广帖子

## 调试和监控

### 启用调试模式
```javascript
CONFIG.ENABLE_LOGGING = true;
```

### 查看性能统计
```javascript
// 在控制台中执行
UAB_AdBlocker.getStats()
// 返回：
// {
//   elementsBlocked: 150,
//   requestsBlocked: 45,
//   uptime: 300000,
//   elementsPerMinute: 30
// }
```

### 常用调试命令
```javascript
// 获取被屏蔽的请求数量
UAB_AdBlocker.getBlockedRequests()

// 获取隐藏的元素数量
UAB_AdBlocker.getHiddenElements()

// 强制执行清理
UAB_AdBlocker.forceCleanup()

// 切换日志记录
UAB_AdBlocker.toggleLogging()
```

## 故障排除

### 常见问题

**1. 某些广告仍然显示**
- 检查是否为新的广告格式
- 添加自定义选择器到配置中
- 增加BATCH_SIZE以处理更多元素

**2. 页面加载变慢**
- 增加OBSERVER_THROTTLE值
- 减少BATCH_SIZE
- 禁用不需要的功能

**3. 与其他脚本冲突**
- 检查命名空间是否正确
- 确认没有重复的CSS选择器
- 调整初始化时机

### 性能优化建议

**内存使用优化**：
```javascript
// 减少清理间隔
CONFIG.CLEANUP_INTERVAL = 5000;

// 减少批处理大小
CONFIG.BATCH_SIZE = 25;
```

**CPU使用优化**：
```javascript
// 增加节流间隔
CONFIG.OBSERVER_THROTTLE = 300;

// 禁用性能监控
CONFIG.ENABLE_PERFORMANCE_MONITORING = false;
```

## 更新和维护

### 定期更新建议
1. **每月更新**广告模式列表
2. **监控新的**广告网络和格式
3. **测试主要网站**的屏蔽效果
4. **调整性能参数**以适应设备变化

### 自定义扩展
可以通过修改CONFIG对象来添加：
- 新的广告网络模式
- 特定网站的选择器
- 自定义屏蔽规则
- 站点特定的处理逻辑

这个全局广告屏蔽脚本提供了企业级的广告屏蔽能力，同时保持了与现有YouTube脚本的完美兼容性。
